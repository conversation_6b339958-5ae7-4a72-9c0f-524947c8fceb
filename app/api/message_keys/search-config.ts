// Search configuration message keys for labels and descriptions
export const SEARCH_CONFIG_KEYS = {
  // Contact filters
  CONTACT_NAME: "search_config.contact.name",
  CONTACT_PHONE: "search_config.contact.phone",
  CONTACT_EMAIL: "search_config.contact.email",
  CONTACT_HAS_EMAIL: "search_config.contact.has_email",
  CONTACT_TAGS: "search_config.contact.tags",
  CONTACT_STATUS: "search_config.contact.status",
  CONTACT_CREATED_BY: "search_config.contact.created_by",
  CONTACT_CREATED_DATE: "search_config.contact.created_date",
  CONTACT_UPDATED_DATE: "search_config.contact.updated_date",
  CONTACT_HAS_PHONE: "search_config.contact.has_phone",
  CONTACT_HAS_NOTES: "search_config.contact.has_notes",

  // Sales filters
  SALES_CUSTOMER_NAME: "search_config.sales.customer_name",
  SALES_CUSTOMER_EMAIL: "search_config.sales.customer_email",
  SALES_PRODUCT_NAME: "search_config.sales.product_name",
  SALES_AMOUNT: "search_config.sales.amount",
  SALES_STATUS: "search_config.sales.status",
  SALES_REP: "search_config.sales.sales_rep",
  SALES_PAYMENT_METHOD: "search_config.sales.payment_method",
  SALES_DATE: "search_config.sales.sale_date",
  SALES_DELIVERY_DATE: "search_config.sales.delivery_date",
  SALES_IS_RECURRING: "search_config.sales.is_recurring",
  SALES_HAS_DISCOUNT: "search_config.sales.has_discount",
  SALES_REGION: "search_config.sales.region",

  // Sort options
  SORT_NAME: "search_config.sort.name",
  SORT_EMAIL: "search_config.sort.email",
  SORT_PHONE: "search_config.sort.phone",
  SORT_CREATED_DATE: "search_config.sort.created_date",
  SORT_UPDATED_DATE: "search_config.sort.updated_date",
  SORT_STATUS: "search_config.sort.status",

  // Date filter options
  DATE_TODAY: "search_config.date.today",
  DATE_YESTERDAY: "search_config.date.yesterday",
  DATE_THIS_WEEK: "search_config.date.this_week",
  DATE_LAST_WEEK: "search_config.date.last_week",
  DATE_THIS_MONTH: "search_config.date.this_month",
  DATE_LAST_MONTH: "search_config.date.last_month",
  DATE_THIS_YEAR: "search_config.date.this_year",
  DATE_LAST_YEAR: "search_config.date.last_year",
  DATE_CUSTOM: "search_config.date.custom",
  DATE_ALL: "search_config.date.all",

  // Date filter descriptions
  DATE_TODAY_DESC: "search_config.date.today_desc",
  DATE_YESTERDAY_DESC: "search_config.date.yesterday_desc",
  DATE_THIS_WEEK_DESC: "search_config.date.this_week_desc",
  DATE_LAST_WEEK_DESC: "search_config.date.last_week_desc",
  DATE_THIS_MONTH_DESC: "search_config.date.this_month_desc",
  DATE_LAST_MONTH_DESC: "search_config.date.last_month_desc",
  DATE_THIS_YEAR_DESC: "search_config.date.this_year_desc",
  DATE_LAST_YEAR_DESC: "search_config.date.last_year_desc",
  DATE_CUSTOM_DESC: "search_config.date.custom_desc",
  DATE_ALL_DESC: "search_config.date.all_desc",

  // Status options
  STATUS_ACTIVE: "search_config.status.active",
  STATUS_INACTIVE: "search_config.status.inactive",
  STATUS_PENDING: "search_config.status.pending",
  STATUS_ARCHIVED: "search_config.status.archived",
  STATUS_DELETED: "search_config.status.deleted",

  // Created by options
  CREATED_BY_SYSTEM: "search_config.created_by.system",
  CREATED_BY_ADMIN: "search_config.created_by.admin",
  CREATED_BY_USER: "search_config.created_by.user",
  CREATED_BY_IMPORT: "search_config.created_by.import",
  CREATED_BY_API: "search_config.created_by.api",

  // Boolean options
  BOOLEAN_YES: "search_config.boolean.yes",
  BOOLEAN_NO: "search_config.boolean.no",

  // Placeholders
  PLACEHOLDER_NAME: "search_config.placeholder.name",
  PLACEHOLDER_PHONE: "search_config.placeholder.phone",
  PLACEHOLDER_EMAIL: "search_config.placeholder.email"
} as const;
